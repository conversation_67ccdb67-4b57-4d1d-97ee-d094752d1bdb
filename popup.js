// popup.js
document.getElementById('download').addEventListener('click', () => {
  // Get selected format from radio buttons
  const formatRadio = document.querySelector('input[name="format"]:checked');
  if (!formatRadio) {
    showStatus('Please select a format', 'error');
    return;
  }

  const format = formatRadio.value;
  const downloadButton = document.getElementById('download');

  // Disable button and show loading status
  downloadButton.disabled = true;
  showStatus('Checking video...', 'loading');

  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    const currentTab = tabs[0];

    // Check if we're on a YouTube video page
    if (!currentTab.url || !currentTab.url.includes('youtube.com/watch')) {
      showStatus('Please navigate to a YouTube video page', 'error');
      downloadButton.disabled = false;
      return;
    }

    showStatus('Processing download...', 'loading');

    chrome.runtime.sendMessage({
      action: 'download',
      tabId: currentTab.id,
      format: format
    }, (response) => {
      downloadButton.disabled = false;

      if (chrome.runtime.lastError) {
        showStatus('Download failed: ' + chrome.runtime.lastError.message, 'error');
      } else if (response && response.error) {
        showStatus('Error: ' + response.error, 'error');
      } else {
        showStatus('Download started! Check your downloads folder.', 'success');
        // Hide success message after 3 seconds
        setTimeout(() => {
          hideStatus();
        }, 3000);
      }
    });
  });
});

function showStatus(message, type) {
  const statusDiv = document.getElementById('status');
  statusDiv.textContent = message;
  statusDiv.className = `status ${type}`;
  statusDiv.classList.remove('hidden');
}

function hideStatus() {
  const statusDiv = document.getElementById('status');
  statusDiv.classList.add('hidden');
}

// Add click handlers for radio option divs to make them more clickable
document.querySelectorAll('.radio-option').forEach(option => {
  option.addEventListener('click', (e) => {
    if (e.target.tagName !== 'INPUT') {
      const radio = option.querySelector('input[type="radio"]');
      radio.checked = true;
    }
  });
});