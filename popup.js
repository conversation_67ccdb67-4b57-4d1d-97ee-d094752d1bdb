// popup.js
document.getElementById('download').addEventListener('click', () => {
  const format = document.getElementById('format').value;
  const loader = document.getElementById('loader');
  loader.style.display = 'block';
  
  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    chrome.runtime.sendMessage({
      action: 'download',
      tabId: tabs[0].id,
      format: format
    }, () => {
      loader.style.display = 'none';
    });
  });
});