# YouTube Downloader - Local Setup Guide

This guide will help you set up a local YouTube downloader that actually works with your browser extension.

## Prerequisites

1. **Node.js** (version 14 or higher)
   - Download from: https://nodejs.org/
   - Verify installation: `node --version`

2. **Python** (for yt-dlp)
   - Download from: https://python.org/
   - Verify installation: `python --version` or `python3 --version`

3. **yt-dlp** (YouTube downloader)
   - Install via pip: `pip install yt-dlp`
   - Or: `pip3 install yt-dlp`
   - Verify installation: `yt-dlp --version`

## Setup Steps

### 1. Install Server Dependencies
```bash
# Navigate to your extension directory
cd c:\Users\<USER>\Desktop\ytdownload

# Install Node.js dependencies
npm install
```

### 2. Start the Local Server
```bash
# Start the server
npm start

# Or for development with auto-restart:
npm run dev
```

The server will start on `http://localhost:3000`

### 3. Load the Extension in Chrome

1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked"
4. Select your extension folder: `c:\Users\<USER>\Desktop\ytdownload`

## How It Works

1. **Local Server**: Runs on localhost:3000 and handles download requests
2. **yt-dlp**: Downloads actual YouTube videos in various formats
3. **Extension**: Sends requests to local server and shows progress
4. **Downloads**: Saved to `~/Downloads/YouTube/` folder

## Usage

1. **Start the server** (must be running before using extension)
2. **Navigate to any YouTube video**
3. **Click the extension icon**
4. **Select your preferred format** (audio or video quality)
5. **Click Download**
6. **Monitor progress** in the extension popup
7. **Find downloaded files** in your `Downloads/YouTube` folder

## Supported Formats

- **Audio**: MP3 (192kbps), WAV
- **Video**: 360p, 480p, 720p HD, 1080p Full HD

## Troubleshooting

### Server Won't Start
- Make sure Node.js is installed: `node --version`
- Install dependencies: `npm install`
- Check if port 3000 is available

### yt-dlp Not Found
- Install Python: https://python.org/
- Install yt-dlp: `pip install yt-dlp`
- Restart your terminal/command prompt

### Extension Shows "Could not connect to local server"
- Make sure the server is running: `npm start`
- Check server is accessible: visit `http://localhost:3000/health`
- Restart the extension in Chrome

### Downloads Fail
- Check yt-dlp is working: `yt-dlp --version`
- Try downloading manually: `yt-dlp [youtube-url]`
- Check server logs for error messages

## Server Endpoints

- `POST /download` - Start a download
- `GET /status/:downloadId` - Check download progress
- `GET /downloads` - List all downloads
- `GET /health` - Server health check

## Legal Notice

This tool is for personal use only. Please respect YouTube's Terms of Service and copyright laws. Only download content you have permission to download.

## File Locations

- **Downloads**: `~/Downloads/YouTube/`
- **Server logs**: Console output
- **Extension**: Chrome Extensions page

## Stopping the Server

Press `Ctrl+C` in the terminal where the server is running.
