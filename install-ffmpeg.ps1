# PowerShell script to install FFmpeg on Windows
# Run as Administrator

Write-Host "FFmpeg Installation Script" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "ERROR: This script must be run as Administrator" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if FFmpeg is already installed
try {
    $ffmpegVersion = & ffmpeg -version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ FFmpeg is already installed!" -ForegroundColor Green
        Write-Host "Version info:" -ForegroundColor Cyan
        Write-Host ($ffmpegVersion -split "`n")[0] -ForegroundColor Cyan
        Read-Host "Press Enter to exit"
        exit 0
    }
} catch {
    # FFmpeg not found, continue with installation
}

Write-Host "FFmpeg not found. Installing..." -ForegroundColor Yellow
Write-Host ""

# Try to install using Chocolatey first
Write-Host "Checking for Chocolatey..." -ForegroundColor Cyan
try {
    $chocoVersion = & choco --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Chocolatey found. Installing FFmpeg..." -ForegroundColor Green
        & choco install ffmpeg -y
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ FFmpeg installed successfully via Chocolatey!" -ForegroundColor Green
            Write-Host ""
            Write-Host "Testing installation..." -ForegroundColor Cyan
            & ffmpeg -version | Select-Object -First 1
            Write-Host ""
            Write-Host "✓ Installation complete!" -ForegroundColor Green
            Write-Host "You can now download MP3 and WAV audio files." -ForegroundColor Green
            Read-Host "Press Enter to exit"
            exit 0
        } else {
            Write-Host "✗ Chocolatey installation failed. Trying manual installation..." -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "Chocolatey not found. Installing Chocolatey first..." -ForegroundColor Yellow
    
    # Install Chocolatey
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    try {
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        
        # Refresh environment variables
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        
        Write-Host "✓ Chocolatey installed. Now installing FFmpeg..." -ForegroundColor Green
        & choco install ffmpeg -y
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ FFmpeg installed successfully!" -ForegroundColor Green
            Write-Host ""
            Write-Host "Testing installation..." -ForegroundColor Cyan
            & ffmpeg -version | Select-Object -First 1
            Write-Host ""
            Write-Host "✓ Installation complete!" -ForegroundColor Green
            Write-Host "You can now download MP3 and WAV audio files." -ForegroundColor Green
            Read-Host "Press Enter to exit"
            exit 0
        }
    } catch {
        Write-Host "✗ Automatic installation failed." -ForegroundColor Red
    }
}

# Manual installation fallback
Write-Host ""
Write-Host "Automatic installation failed. Please install manually:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Go to: https://www.gyan.dev/ffmpeg/builds/" -ForegroundColor Cyan
Write-Host "2. Download 'ffmpeg-release-essentials.zip'" -ForegroundColor Cyan
Write-Host "3. Extract to C:\ffmpeg" -ForegroundColor Cyan
Write-Host "4. Add C:\ffmpeg\bin to your PATH environment variable" -ForegroundColor Cyan
Write-Host ""
Write-Host "Or use winget: winget install Gyan.FFmpeg" -ForegroundColor Cyan
Write-Host ""
Write-Host "Alternative: Download video formats instead of audio formats" -ForegroundColor Green
Write-Host "(Video downloads work without FFmpeg)" -ForegroundColor Green

Read-Host "Press Enter to exit"
