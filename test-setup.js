// Test script to verify the setup
const { spawn } = require('child_process');
const http = require('http');

console.log('YouTube Downloader Setup Test');
console.log('=============================\n');

// Test 1: Check Node.js
console.log('✓ Node.js version:', process.version);

// Test 2: Check yt-dlp
console.log('Testing yt-dlp installation...');
const ytdlp = spawn('yt-dlp', ['--version']);

ytdlp.stdout.on('data', (data) => {
    console.log('✓ yt-dlp version:', data.toString().trim());
});

ytdlp.stderr.on('data', (data) => {
    console.log('✗ yt-dlp error:', data.toString());
});

ytdlp.on('close', (code) => {
    if (code === 0) {
        console.log('✓ yt-dlp is working correctly');
    } else {
        console.log('✗ yt-dlp test failed with code:', code);
        console.log('Please install yt-dlp: pip install yt-dlp');
    }
    
    // Test 3: Check if server can start
    console.log('\nTesting server startup...');
    testServer();
});

function testServer() {
    // Try to connect to localhost:3000
    const req = http.get('http://localhost:3000/health', (res) => {
        let data = '';
        res.on('data', (chunk) => {
            data += chunk;
        });
        res.on('end', () => {
            console.log('✓ Server is running and responding');
            console.log('✓ Health check response:', data);
            console.log('\n🎉 Setup test completed successfully!');
            console.log('\nNext steps:');
            console.log('1. Make sure the server is running: npm start');
            console.log('2. Load the extension in Chrome');
            console.log('3. Go to a YouTube video and test downloading');
        });
    });
    
    req.on('error', (err) => {
        console.log('ℹ Server is not running (this is normal if you haven\'t started it yet)');
        console.log('ℹ To start the server, run: npm start');
        console.log('\n✓ Setup test completed!');
        console.log('\nNext steps:');
        console.log('1. Start the server: npm start');
        console.log('2. Load the extension in Chrome');
        console.log('3. Go to a YouTube video and test downloading');
    });
}
