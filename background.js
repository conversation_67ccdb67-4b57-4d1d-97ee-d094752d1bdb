// background.js
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'download') {
      chrome.scripting.executeScript({
        target: {tabId: request.tabId},
        func: fetchVideoInfo,
        args: [request.format]
      });
    }
  });
  
  function fetchVideoInfo(format) {
    const videoId = new URLSearchParams(window.location.search).get('v');
    const title = document.querySelector('h1.title')?.innerText || 'video';
    
    // In a real implementation, you would call your own server here
    // This is a placeholder for demonstration purposes
    const dummyUrls = {
      mp3: `https://example.com/convert?id=${videoId}&format=mp3`,
      wav: `https://example.com/convert?id=${videoId}&format=wav`,
      360: `https://example.com/convert?id=${videoId}&format=mp4&quality=360`,
      480: `https://example.com/convert?id=${videoId}&format=mp4&quality=480`,
      720: `https://example.com/convert?id=${videoId}&format=mp4&quality=720`,
      1080: `https://example.com/convert?id=${videoId}&format=mp4&quality=1080`
    };
  
    chrome.runtime.sendMessage({
      action: 'startDownload',
      url: dummyUrls[format],
      filename: `${title.replace(/[^a-z0-9]/gi, '_').substring(0, 50)}.${format}`
    });
  }
  
  chrome.runtime.onMessage.addListener((request) => {
    if (request.action === 'startDownload') {
      chrome.downloads.download({
        url: request.url,
        filename: request.filename,
        conflictAction: 'uniquify'
      });
    }
  });