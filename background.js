// background.js
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.action === 'download') {
    // Execute script in current tab
    chrome.scripting.executeScript({
      target: {tabId: request.tabId},
      func: fetchVideoInfo,
      args: [request.format]
    }, () => {
      if (chrome.runtime.lastError) {
        sendResponse({error: chrome.runtime.lastError.message});
      }
    });
    return true; // Keep message channel open for async response
  }
});

function fetchVideoInfo(format) {
  try {
    // Get video ID from URL parameters
    const videoId = new URLSearchParams(window.location.search).get('v');
    if (!videoId) {
      chrome.runtime.sendMessage({
        action: 'downloadError',
        error: 'Could not find video ID. Make sure you are on a YouTube video page.'
      });
      return;
    }

    // Get the full YouTube URL
    const videoUrl = window.location.href;

    // Try multiple selectors for video title
    const titleSelectors = [
      'h1.title',
      'h1[class*="title"]',
      'h1.ytd-video-primary-info-renderer',
      'h1.ytd-watch-metadata',
      'yt-formatted-string[class*="title"]'
    ];

    let title = 'video';
    for (const selector of titleSelectors) {
      const titleElement = document.querySelector(selector);
      if (titleElement && titleElement.textContent.trim()) {
        title = titleElement.textContent.trim();
        break;
      }
    }

    // Send download request to local server
    chrome.runtime.sendMessage({
      action: 'startLocalDownload',
      url: videoUrl,
      format: format,
      title: title,
      videoId: videoId
    });

  } catch (error) {
    chrome.runtime.sendMessage({
      action: 'downloadError',
      error: 'Failed to extract video information: ' + error.message
    });
  }
}

chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.action === 'startLocalDownload') {
    // Send download request to local server
    fetch('http://localhost:3000/download', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: request.url,
        format: request.format,
        quality: request.format // For video formats like 360, 480, etc.
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.error) {
        sendResponse({error: data.error});
      } else {
        // Start polling for download status
        pollDownloadStatus(data.downloadId, sendResponse);
      }
    })
    .catch(error => {
      console.error('Server communication error:', error);
      sendResponse({
        error: 'Could not connect to local server. Make sure the server is running on localhost:3000'
      });
    });

    return true; // Keep message channel open for async response
  }

  if (request.action === 'downloadError') {
    // Send error back to popup
    chrome.runtime.sendMessage({
      action: 'showError',
      error: request.error
    });
  }
});

// Poll download status from local server
function pollDownloadStatus(downloadId, sendResponse) {
  const checkStatus = () => {
    fetch(`http://localhost:3000/status/${downloadId}`)
      .then(response => response.json())
      .then(data => {
        if (data.status === 'completed') {
          sendResponse({
            success: true,
            message: `Download completed: ${data.title}`,
            progress: 100
          });
        } else if (data.status === 'error') {
          sendResponse({
            error: data.error || 'Download failed'
          });
        } else {
          // Still downloading, send progress update
          chrome.runtime.sendMessage({
            action: 'downloadProgress',
            progress: data.progress,
            title: data.title,
            status: data.status
          });

          // Continue polling
          setTimeout(checkStatus, 2000);
        }
      })
      .catch(error => {
        console.error('Status check error:', error);
        sendResponse({
          error: 'Lost connection to download server'
        });
      });
  };

  // Start checking status
  setTimeout(checkStatus, 1000);
}