// background.js
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'download') {
    chrome.scripting.executeScript({
      target: {tabId: request.tabId},
      func: fetchVideoInfo,
      args: [request.format]
    }, () => {
      if (chrome.runtime.lastError) {
        sendResponse({error: chrome.runtime.lastError.message});
      }
    });
    return true; // Keep message channel open for async response
  }
});

function fetchVideoInfo(format) {
  try {
    const videoId = new URLSearchParams(window.location.search).get('v');
    if (!videoId) {
      chrome.runtime.sendMessage({
        action: 'downloadError',
        error: 'Could not find video ID. Make sure you are on a YouTube video page.'
      });
      return;
    }

    // Try multiple selectors for video title
    const titleSelectors = [
      'h1.title',
      'h1[class*="title"]',
      'h1.ytd-video-primary-info-renderer',
      'h1.ytd-watch-metadata',
      'yt-formatted-string[class*="title"]'
    ];

    let title = 'video';
    for (const selector of titleSelectors) {
      const titleElement = document.querySelector(selector);
      if (titleElement && titleElement.textContent.trim()) {
        title = titleElement.textContent.trim();
        break;
      }
    }

    // Clean up title for filename
    const cleanTitle = title
      .replace(/[<>:"/\\|?*]/g, '') // Remove invalid filename characters
      .replace(/\s+/g, '_') // Replace spaces with underscores
      .substring(0, 50); // Limit length

    // Determine file extension based on format
    let extension;
    if (format === 'mp3' || format === 'wav') {
      extension = format;
    } else {
      extension = 'mp4';
    }

    // IMPORTANT: This is a demo implementation
    // In a real extension, you would need to:
    // 1. Set up a backend service that can actually download/convert YouTube videos
    // 2. Use legitimate APIs or tools like yt-dlp
    // 3. Handle YouTube's terms of service appropriately

    chrome.runtime.sendMessage({
      action: 'showDemoWarning',
      videoId: videoId,
      title: cleanTitle,
      format: format,
      extension: extension
    });

  } catch (error) {
    chrome.runtime.sendMessage({
      action: 'downloadError',
      error: 'Failed to extract video information: ' + error.message
    });
  }
}

chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.action === 'startDownload') {
    chrome.downloads.download({
      url: request.url,
      filename: request.filename,
      conflictAction: 'uniquify'
    }, (downloadId) => {
      if (chrome.runtime.lastError) {
        console.error('Download failed:', chrome.runtime.lastError);
        sendResponse({error: chrome.runtime.lastError.message});
      } else {
        console.log('Download started with ID:', downloadId);
        sendResponse({success: true, downloadId: downloadId});
      }
    });
    return true; // Keep message channel open for async response
  }

  if (request.action === 'downloadError') {
    // Send error back to popup
    chrome.runtime.sendMessage({
      action: 'showError',
      error: request.error
    });
  }

  if (request.action === 'showDemoWarning') {
    // Create a demo file to show the functionality works
    // This creates a text file with video information instead of actual video
    const demoContent = `YouTube Video Information
========================
Video ID: ${request.videoId}
Title: ${request.title}
Requested Format: ${request.format}
Timestamp: ${new Date().toISOString()}

NOTE: This is a demo implementation.
To actually download YouTube videos, you would need:
1. A backend service with proper YouTube API integration
2. Compliance with YouTube's Terms of Service
3. Proper video conversion tools

The original extension was trying to download from non-existent URLs,
which is why you were getting "_______.html" files.`;

    // Create a blob URL for the demo content
    const blob = new Blob([demoContent], {type: 'text/plain'});
    const url = URL.createObjectURL(blob);

    chrome.downloads.download({
      url: url,
      filename: `${request.title}_${request.format}_demo.txt`,
      conflictAction: 'uniquify'
    }, (downloadId) => {
      // Clean up the blob URL after download starts
      setTimeout(() => URL.revokeObjectURL(url), 1000);

      if (chrome.runtime.lastError) {
        console.error('Demo download failed:', chrome.runtime.lastError);
      } else {
        console.log('Demo file download started with ID:', downloadId);
      }
    });
  }
});