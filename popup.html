<!-- popup.html -->
<!DOCTYPE html>
<html>
<head>
  <style>
    body {
      width: 320px;
      padding: 15px;
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
    }

    h3 {
      margin-top: 0;
      color: #333;
      text-align: center;
    }

    .format-group {
      background: white;
      border-radius: 8px;
      padding: 15px;
      margin: 10px 0;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .format-section {
      margin-bottom: 15px;
    }

    .format-section h4 {
      margin: 0 0 8px 0;
      color: #555;
      font-size: 14px;
      font-weight: bold;
    }

    .radio-option {
      display: flex;
      align-items: center;
      margin: 6px 0;
      padding: 8px;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .radio-option:hover {
      background-color: #f0f0f0;
    }

    .radio-option input[type="radio"] {
      margin-right: 10px;
      transform: scale(1.2);
    }

    .radio-option label {
      cursor: pointer;
      flex-grow: 1;
      font-size: 14px;
    }

    button {
      width: 100%;
      padding: 12px;
      margin: 15px 0 10px 0;
      background-color: #ff0000;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    button:hover {
      background-color: #cc0000;
    }

    button:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }

    .status {
      text-align: center;
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      font-size: 14px;
    }

    .status.loading {
      background-color: #e3f2fd;
      color: #1976d2;
    }

    .status.error {
      background-color: #ffebee;
      color: #c62828;
    }

    .status.success {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .hidden { display: none; }
  </style>
</head>
<body>
  <h3>YouTube Downloader</h3>

  <div class="format-group">
    <div class="format-section">
      <h4>Audio Formats</h4>
      <div class="radio-option">
        <input type="radio" id="mp3" name="format" value="mp3" checked>
        <label for="mp3">MP3 Audio</label>
      </div>
      <div class="radio-option">
        <input type="radio" id="wav" name="format" value="wav">
        <label for="wav">WAV Audio</label>
      </div>
    </div>

    <div class="format-section">
      <h4>Video Formats</h4>
      <div class="radio-option">
        <input type="radio" id="360p" name="format" value="360">
        <label for="360p">360p Video</label>
      </div>
      <div class="radio-option">
        <input type="radio" id="480p" name="format" value="480">
        <label for="480p">480p Video</label>
      </div>
      <div class="radio-option">
        <input type="radio" id="720p" name="format" value="720">
        <label for="720p">720p Video (HD)</label>
      </div>
      <div class="radio-option">
        <input type="radio" id="1080p" name="format" value="1080">
        <label for="1080p">1080p Video (Full HD)</label>
      </div>
    </div>
  </div>

  <button id="download">Download</button>
  <div class="status hidden" id="status"></div>

  <script src="popup.js"></script>
</body>
</html>