const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

const app = express();
const PORT = 3000;

// Enable CORS for extension communication
app.use(cors());
app.use(express.json());

// Store active downloads
const activeDownloads = new Map();

// Default download directory
const DOWNLOAD_DIR = path.join(os.homedir(), 'Downloads', 'YouTube');

// Ensure download directory exists
if (!fs.existsSync(DOWNLOAD_DIR)) {
    fs.mkdirSync(DOWNLOAD_DIR, { recursive: true });
}

app.post('/download', async (req, res) => {
    const { url, format, quality } = req.body;
    
    if (!url) {
        return res.status(400).json({ error: 'URL is required' });
    }
    
    // Generate unique download ID
    const downloadId = Date.now().toString();
    
    try {
        // Validate YouTube URL
        if (!url.includes('youtube.com/watch') && !url.includes('youtu.be/')) {
            return res.status(400).json({ error: 'Invalid YouTube URL' });
        }
        
        // Prepare yt-dlp command
        let ytDlpArgs = [
            url,
            '--output', path.join(DOWNLOAD_DIR, '%(title)s.%(ext)s'),
            '--no-playlist',
            '--embed-subs',
            '--write-auto-sub',
            '--sub-lang', 'en',
            '--ignore-errors'
        ];
        
        // Add format-specific arguments
        if (format === 'mp3') {
            ytDlpArgs.push('--extract-audio', '--audio-format', 'mp3', '--audio-quality', '192K');
        } else if (format === 'wav') {
            ytDlpArgs.push('--extract-audio', '--audio-format', 'wav');
        } else {
            // Video formats
            const qualityMap = {
                '360': 'best[height<=360]',
                '480': 'best[height<=480]',
                '720': 'best[height<=720]',
                '1080': 'best[height<=1080]'
            };
            ytDlpArgs.push('--format', qualityMap[quality] || 'best');
        }
        
        console.log('Starting download with yt-dlp:', ytDlpArgs.join(' '));
        
        // Start yt-dlp process
        const ytDlp = spawn('yt-dlp', ytDlpArgs);
        
        // Store download info
        activeDownloads.set(downloadId, {
            process: ytDlp,
            status: 'downloading',
            progress: 0,
            title: 'Unknown',
            startTime: Date.now()
        });
        
        let downloadInfo = activeDownloads.get(downloadId);
        
        // Handle stdout (progress and info)
        ytDlp.stdout.on('data', (data) => {
            const output = data.toString();
            console.log('yt-dlp stdout:', output);
            
            // Extract title
            const titleMatch = output.match(/\[download\] Destination: (.+)/);
            if (titleMatch) {
                downloadInfo.title = path.basename(titleMatch[1]);
            }
            
            // Extract progress
            const progressMatch = output.match(/\[download\]\s+(\d+\.?\d*)%/);
            if (progressMatch) {
                downloadInfo.progress = parseFloat(progressMatch[1]);
            }
        });
        
        // Handle stderr (errors and additional info)
        ytDlp.stderr.on('data', (data) => {
            const error = data.toString();
            console.log('yt-dlp stderr:', error);
            
            if (error.includes('ERROR')) {
                downloadInfo.status = 'error';
                downloadInfo.error = error;
            }
        });
        
        // Handle process completion
        ytDlp.on('close', (code) => {
            console.log(`yt-dlp process exited with code ${code}`);
            
            if (code === 0) {
                downloadInfo.status = 'completed';
                downloadInfo.progress = 100;
            } else {
                downloadInfo.status = 'error';
                downloadInfo.error = `Download failed with exit code ${code}`;
            }
        });
        
        // Return download ID immediately
        res.json({ 
            downloadId, 
            status: 'started',
            message: 'Download started successfully'
        });
        
    } catch (error) {
        console.error('Download error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Get download status
app.get('/status/:downloadId', (req, res) => {
    const { downloadId } = req.params;
    const download = activeDownloads.get(downloadId);
    
    if (!download) {
        return res.status(404).json({ error: 'Download not found' });
    }
    
    res.json({
        downloadId,
        status: download.status,
        progress: download.progress,
        title: download.title,
        error: download.error
    });
});

// Get all active downloads
app.get('/downloads', (req, res) => {
    const downloads = Array.from(activeDownloads.entries()).map(([id, info]) => ({
        downloadId: id,
        status: info.status,
        progress: info.progress,
        title: info.title,
        startTime: info.startTime
    }));
    
    res.json(downloads);
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        downloadDir: DOWNLOAD_DIR,
        activeDownloads: activeDownloads.size
    });
});

// Clean up completed downloads periodically
setInterval(() => {
    for (const [id, download] of activeDownloads.entries()) {
        if (download.status === 'completed' || download.status === 'error') {
            // Remove after 5 minutes
            if (Date.now() - download.startTime > 5 * 60 * 1000) {
                activeDownloads.delete(id);
            }
        }
    }
}, 60000); // Check every minute

app.listen(PORT, () => {
    console.log(`YouTube Downloader Server running on http://localhost:${PORT}`);
    console.log(`Downloads will be saved to: ${DOWNLOAD_DIR}`);
    console.log('Make sure yt-dlp is installed: pip install yt-dlp');
});
