# FFmpeg Installation Guide

FFmpeg is required for audio conversion (MP3, WAV formats). Video downloads work without FFmpeg.

## Windows Installation (Recommended)

### Option 1: Using Chocolatey (Easiest)
```bash
# Install Chocolatey first (if not installed)
# Run PowerShell as Administrator and paste:
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Install FFmpeg
choco install ffmpeg
```

### Option 2: Manual Installation
1. **Download FFmpeg**:
   - Go to: https://www.gyan.dev/ffmpeg/builds/
   - Download "release builds" → "ffmpeg-release-essentials.zip"

2. **Extract and Install**:
   ```bash
   # Extract to C:\ffmpeg
   # Add C:\ffmpeg\bin to your PATH environment variable
   ```

3. **Add to PATH**:
   - Press `Win + R`, type `sysdm.cpl`, press Enter
   - Click "Environment Variables"
   - Under "System Variables", find "Path", click "Edit"
   - Click "New" and add: `C:\ffmpeg\bin`
   - Click "OK" on all dialogs
   - **Restart your command prompt/terminal**

### Option 3: Using Winget
```bash
winget install Gyan.FFmpeg
```

## Verify Installation

```bash
# Test FFmpeg installation
ffmpeg -version

# Should show version information
```

## Alternative: Download Video Formats Instead

If you don't want to install FFmpeg, you can:
- Download videos in MP4 format (360p, 480p, 720p, 1080p)
- Use online converters later if needed
- The extension will work perfectly for video downloads

## Troubleshooting

### "ffmpeg not found" error
- Make sure FFmpeg is in your PATH
- Restart your terminal/command prompt after installation
- Restart the YouTube downloader server: `npm start`

### Permission errors
- Run command prompt as Administrator when installing
- Make sure you have write permissions to the installation directory

### Still not working?
- Try downloading video formats instead of audio
- Check if FFmpeg is properly installed: `ffmpeg -version`
- Restart your computer after PATH changes

## What FFmpeg Does

- **With FFmpeg**: Can convert to MP3, WAV, and optimize video formats
- **Without FFmpeg**: Can download videos in their original format (usually WebM/MP4)

The YouTube downloader will work either way, but audio conversion requires FFmpeg.
