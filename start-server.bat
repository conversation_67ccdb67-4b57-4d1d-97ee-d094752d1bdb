@echo off
echo YouTube Downloader Server
echo ========================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if yt-dlp is installed
yt-dlp --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: yt-dlp is not installed
    echo Please install it with: pip install yt-dlp
    pause
    exit /b 1
)

REM Check if dependencies are installed
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

echo Starting YouTube Downloader Server...
echo Server will be available at: http://localhost:3000
echo.
echo To stop the server, press Ctrl+C
echo.

REM Start the server
npm start
